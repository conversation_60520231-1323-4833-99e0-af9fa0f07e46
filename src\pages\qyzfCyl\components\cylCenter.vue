<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>中间地图</title>
  <script src="/Vue/vue.js"></script>
  <script src="/echarts/echarts4.4.min.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <style>
    body,
    p {
      padding: 0;
      margin: 0;
    }

    .map_center {
      width: 3800px;
      height: 2160px;
      /* background-image: url(/static/citybrain/qyhx/images/index/bg20230602.png); */
      background-size: 100% 100%;
    }

    .map-echart {
      width: 100%;
      height: 100%;
      /* background-image: url(/static/citybrain/qyhx/images/index/bg20230602.png); */
      background-size: 100% 100%;
    }

    .list-box {
      width: 1530px;
      height: 150px;
      position: absolute;
      left: calc(50% - 765px);
    }

    .list-bg {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-image: url('/static/citybrain/qyhx/images/index/search-list.png');
      background-size: 100% 100%;
      opacity: 0.3;
      z-index: 0;
    }

    .list {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      font-size: 40px;
      color: #bad3ff;
      line-height: 46px;
      position: absolute;
      z-index: 1;
    }

    .list-itemO {
      flex: 0.38;
      margin: 0 20px;
      text-align: center;
      padding-top: 15px;
      box-sizing: border-box;
    }

    .list-item {
      flex: 0.333;
      margin: 0 20px;
      text-align: center;
      padding-top: 15px;
      box-sizing: border-box;
    }


    .cursor {
      cursor: pointer;
    }

    .value-css {
      font-size: 50px;
      font-weight: bold;
      margin-top: 25px;
      display: inline-block;
      background: linear-gradient(180deg, #ff4f1d 0%, #ffa41d 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .icon {
      width: 40px;
      height: 33px;
      background: url('/static/citybrain/qyzf/img/cyl/down.png') no-repeat 0px 0px;
    }

    .cyl-list {
      position: absolute;
      left: 100px;
      top: 100px;
      z-index: 99;
    }

    .cyl-list-item {
      width: 300px;
      /* height:100px; */
      background: url('/static/citybrain/qyzf/img/cyl/cyl_bg.png') no-repeat;
      background-size: 100% 100%;
      padding: 30px 30px;
      box-sizing: border-box;
      margin-bottom: 40px;
      cursor: pointer;
    }

    .active {
      background: url('/static/citybrain/qyzf/img/cyl/cyl_active.png') no-repeat;
      background-size: 100% 100%;
    }

    .cyl-list-item>p {
      font-size: 32px;
      color: #fff;
      white-space: wrap;
      line-height: 50px;
    }
  </style>
</head>

<body>
  <div id="app">
    <!-- 头部列表 -->
    <div class="list-box" v-if="cylIndex == -1">
      <div class="list">
        <div class="list-itemO" v-for="(item,index) in listDataTotal">
          <p>{{item.name}}</p>
          <span class="value-css">
            <count-to :start-val="0" :end-val="Number(item.value)" :duration="3000" :decimals="index == 1 ? 2 : 0"
              class="s-font-50"></count-to>
            {{item.unit}}
          </span>
          <img src="/static/citybrain/qyzf/img/cyl/sq3.png" alt="" width="30" style="margin-left:20px;">
          <span style="color: #BAD3FF !important;font-size: 32px;">{{item.zs}}%</span>
        </div>
      </div>
      <div class="list-bg"></div>
    </div>

    <div class="list-box" v-else>
      <div class="list">
        <div class="list-item" v-for="(item,index) in listData">
          <p>{{item.name}}</p>
          <span class="value-css" v-if="item.value != '-'">
            <count-to :start-val="0" :end-val="Number(item.value)" :duration="3000" class="s-font-50"
              :decimals="index == 1 ? 2 : 0"></count-to>
            {{item.unit}}
          </span>
          <span class="value-css" v-else>{{item.value}}</span>
          <img src="/static/citybrain/qyzf/img/cyl/sq3.png" alt="" width="30" style="margin-left:20px;">
          <span style="color: #BAD3FF !important;font-size: 32px;">{{item.zs}}%</span>
        </div>
      </div>
      <div class="list-bg"></div>
    </div>


    <!-- 左侧列表 -->
    <div class="cyl-list">
      <div class="cyl-list-item" v-for="(item,index) in cylList" @click="changeCyl(index,item)"
        :class="index==cylIndex?'active':''">
        <p>{{item.lzqy}}</p>
      </div>
    </div>

    <div class="map_center" id="ecartDiv" style="z-index: 9">
      <div ref="myEchart" class="map-echart"></div>
      <img ref="mapSvg" id="mapSvg" v-show="false" hidden src="/static/citybrain/3Dmap/img/7680.jpg" />
    </div>
  </div>


  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    var center = new Vue({
      el: "#app",
      data: {
        listDataTotal: [{
          name: '规上企业总数',
          value: '0',
          unit: '家',
          zs: '-'
        },
        {
          name: '规上总产值',
          value: '0',
          unit: '亿元',
          zs: '-'
        }],
        listData: [{
          name: '规上企业数',
          value: '0',
          unit: '家',
          zs: '-'
        },
        {
          name: '规上企业产值',
          value: '0',
          unit: '亿元',
          zs: '-'
        },
          // {
          //   name: '培育指数',
          //   value: '-',
          //   unit: '',
          //   zs: '-'
          // }
        ],
        cylIndex: -1,
        cylList: [{
          type: '1',
          cylId: 104,
          lzqy: '新能源汽车及关键零部件',
          lzhbqy: '396',
          sxyqy: '222.98',
          sxyqyZs: '30',
          sjqx: '-',
        },
        {
          type: '2',
          cylId: 103,
          lzqy: '智能光伏及新型储能',
          lzhbqy: '308',
          sxyqy: '291.32',
          sxyqyZs: '-27.5',
          sjqx: '-',
        },
        {
          type: '3',
          cylId: 211,
          lzqy: '电动工具',
          lzhbqy: '460',
          sxyqy: '131.14',
          sxyqyZs: '17.5',
          sjqx: '-',
        },
        {
          type: '4',
          cylId: 102,
          lzqy: '纺织服装',
          lzhbqy: '1527',
          sxyqy: '244.34',
          sxyqyZs: '10',
          sjqx: '-',
        },
        {
          type: '5',
          cylId: 106,
          lzqy: '生物医药及植入性医疗器械',
          lzhbqy: '107',
          sxyqy: '55.72',
          sxyqyZs: '-5.2',
          sjqx: '-',
        },
        {
          type: '6',
          cylId: 212,
          lzqy: '磁性材料',
          lzhbqy: '121',
          sxyqy: '65.55',
          sxyqyZs: '-7.4',
          sjqx: '-',
        },
        {
          type: '7',
          cylId: 214,
          lzqy: '集成电路及信创',
          lzhbqy: '31',
          sxyqy: '20.23',
          sxyqyZs: '45.9',
          sjqx: '-',
        },
        {
          type: '8',
          cylId: 215,
          lzqy: '电子化学品',
          lzhbqy: '31',
          sxyqy: '16.3',
          sxyqyZs: '3.5',
          sjqx: '-',
        },
        {
          type: '9',
          cylId: 216,
          lzqy: '工业机床',
          lzhbqy: '154',
          sxyqy: '20.28',
          sxyqyZs: '1.8',
          sjqx: '-',
        },
        {
          type: '10',
          cylId: 217,
          lzqy: '机器人',
          lzhbqy: '44',
          sxyqy: '20.17',
          sxyqyZs: '6.9',
          sjqx: '-',
        }],
        animationData: [
          {
            city: '金华市',
            num: 0,
            point: [119.95, 29.17],
          },
          {
            city: '婺城区',
            num: 0,
            point: [119.52, 29.029999],
          },
          {
            city: '开发区',
            num: 0,
            point: [119.52, 29.099999],
          },
          {
            city: '金东区',
            num: 0,
            point: [119.799596, 29.29],
          },
          {
            city: '东阳市',
            num: 0,
            point: [120.25, 29.1],
          },
          {
            city: '义乌市',
            num: 0,
            point: [120.25991, 29.366614],
          },
          {
            city: '兰溪市',
            num: 0,
            point: [119.45, 29.4],
          },
          {
            city: '浦江县',
            num: 0,
            point: [119.903937, 29.55],
          },

          {
            city: '武义县',
            num: 0,
            point: [119.5, 28.768287],
          },
          {
            city: '永康市',
            num: 0,
            point: [120.07, 28.824317],
          },
          {
            city: '磐安县',
            num: 0,
            point: [120.629672, 28.820893],
          }
        ],
      },

      mounted () {
        this.getdata()
      },

      methods: {
        changeCyl (index, item) {
          this.cleanMap()
          this.animationData.forEach((i) => { i.num = 0 })
          if (this.cylIndex == index) {
            this.cylIndex = -1
            $api('csdn_qyhx53', { sscyl: '' }).then(res => {
              // console.log('传空的时候的数据', res)
              let total = 0
              this.animationData.forEach(item => {
                res.forEach(i => {
                  if (item.city == i.qx) {
                    item.num = i.zs
                    total += Number(i.zs)
                  }
                })
              })
              this.listDataTotal[0].value = total
              this.initEcharts()
            })
            $api('csdn_qyhx68', { cyl_name: '100' }).then(res => {
              this.listDataTotal[1].value = res[0].gsgyzcz
              this.listDataTotal[1].zs = res[0].gsgyzczzs
            })
          } else {
            this.cylIndex = index

            // this.listData[2].value = item.sjqx

            $api('csdn_qyhx53', { sscyl: item.cylId }).then(res => {
              let total = 0
              this.animationData.forEach(item => {
                res.forEach(i => {
                  if (item.city == i.qx) {
                    item.num = i.zs
                    total += Number(i.zs)
                  }
                })
              })
              this.listData[0].value = total
              this.initEcharts()
            })
            $api('csdn_qyhx68', { cyl_name: item.cylId }).then(res => {
              // console.log('分区指数', res)
              this.listData[1].value = res[0].gsgyzcz
              this.listData[1].zs = res[0].gsgyzczzs
            })
            // console.log('执行到这儿', window.parent)
            // window.top.postMessage({ type: 'toP', cylId: item.cylId }, '*')
          }
        },

        cleanMap () {
          let newOption = JSON.parse(JSON.stringify(this.myChartMap.getOption()))
          newOption.series = [{
            type: 'map',
            map: 'jh',
            zoom: 1.2,
            roam: false,
            layoutCenter: ['45%', '45%'],
            layoutSize: '90%',
            aspectScale: 1,
            selectedMode: false,
            label: {
              //初始标签样式
              show: true,
              color: '#fff',
              position: 'inside',
              distance: 0,
              fontSize: 50,
            },
            itemStyle: {
              areaColor: {
                image: this.$refs.mapSvg, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串
                repeat: 'repeat', // 是否平铺, 可以是 'repeat-x', 'repeat-y', 'no-repeat'
              },
              borderColor: '#70e8e9',
              borderWidth: 3,
              emphasis: {
                label: {
                  show: true,
                  color: '#fff',
                  fontSize: 50,
                },
                areaColor: {
                  image: this.$refs.mapSvg, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串
                  repeat: 'repeat', // 是否平铺, 可以是 'repeat-x', 'repeat-y', 'no-repeat'
                },
                borderColor: '#3fdaff',
                borderWidth: 10,
              },
            },
            zlevel: 99,
            data: [],
          }]
          this.myChartMap.setOption(newOption, { notMerge: true })
        },

        getdata () {  // 地图首次加载
          async function getData () {
            let res = await fetch('/static/data/bounds_2dmap.json')
            return res.json()
          }
          getData().then((result) => {
            echarts.registerMap('jh', result)
            this.myChartMap = echarts.init(this.$refs.myEchart)
            $api('csdn_qyhx53', { sscyl: '' }).then(res => {
              let total = 0
              // console.log('传空的时候的数据', res)
              this.animationData.forEach(item => {
                res.forEach(i => {
                  if (item.city == i.qx) {
                    item.num = i.zs
                    total += Number(i.zs)
                  }
                })
              })
              this.listDataTotal[0].value = total
              this.initEcharts()
            })
            $api('csdn_qyhx68', { cyl_name: '100' }).then(res => {
              // console.log('规上总产值', res)
              this.listDataTotal[1].value = res[0].gsgyzcz
              this.listDataTotal[1].zs = res[0].gsgyzczzs
            })
          })
        },

        initEcharts () {
          let _this = this
          this.$nextTick(() => {
            let option = {
              tooltip: {
                show: false
              },
              geo: [{
                show: true,
                map: 'jh',
                zoom: 1.2,
                roam: false,
                regions: [],
                zlevel: 5,
                layoutCenter: ['45%', '45%'],
                layoutSize: '90%',
                aspectScale: 1,
                itemStyle: {
                  areaColor: 'transparent',
                },
                silent: true,
              },
              {
                show: true,
                map: 'jh',
                zoom: 1.2,
                roam: false,
                zlevel: 4,
                layoutCenter: ['45.2%', '45.2%'],
                layoutSize: '90%',
                aspectScale: 1,
                itemStyle: {
                  borderWidth: 1,
                  borderColor: 'rgba(22, 186, 212,0.8)',
                  shadowColor: 'rgba(80, 183, 140,0.5)',
                  shadowOffsetY: 5,
                  shadowBlur: 15,
                  areaColor: 'rgba(5,21,35,0.1)',
                },
                silent: true,
              },
              {
                show: true,
                map: 'jh',
                zoom: 1.2,
                roam: false,
                zlevel: 3,
                layoutCenter: ['45.3%', '45.3%'],
                layoutSize: '90%',
                aspectScale: 1,
                itemStyle: {
                  borderWidth: 6,
                  borderColor: 'rgba(29,111,165,1)',
                  shadowColor: 'rgba(29,111,165,0.5)',
                  shadowOffsetY: 15,
                  shadowBlur: 8,
                  areaColor: 'rgba(5,21,35,0.8)',
                },
                silent: true,
              }],
              series: [] // 数据
            }

            this.animationData.forEach(item => {
              let obj = {
                type: 'scatter',
                tooltip: {
                  show: false,
                },
                zlevel: 999,
                rippleEffect: {
                  brushType: 'stroke',
                },
                coordinateSystem: 'geo',
                symbol: 'image:///static/citybrain/qyzf/img/cyl/map_bg.png',
                symbolSize: [530, 100],
                data: [{
                  name: item.city,
                  value: item.point,
                  obj: item
                },],
                label: {
                  position: "right",
                  show: true,
                  color: "#fff",
                  fontFamily: "微软雅黑",
                  padding: [0, 0, 0, -520],
                  align: "left",
                  width: 200,
                  height: 35,
                  formatter: function (params) {
                    return `{b|${params.data.obj.city}` + '      企业：' + `${params.data.obj.num}}`
                  },
                  rich: {
                    b: {
                      align: "left",
                      lineHeight: 35,
                      fontSize: 45,
                      color: "#fff",
                    },
                  },
                },
                itemStyle: {
                  color: "transparent",
                },
              }
              option.series.push(obj)
            })

            option.series.push({
              type: 'map',
              map: 'jh',
              zoom: 1.2,
              roam: false,
              layoutCenter: ['45%', '45%'],
              layoutSize: '90%',
              aspectScale: 1,
              selectedMode: false,
              label: {
                //初始标签样式
                show: true,
                color: '#fff',
                position: 'inside',
                distance: 0,
                fontSize: 50,
              },
              itemStyle: {
                areaColor: {
                  image: this.$refs.mapSvg, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串
                  repeat: 'repeat', // 是否平铺, 可以是 'repeat-x', 'repeat-y', 'no-repeat'
                },
                borderColor: '#70e8e9',
                borderWidth: 3,
                emphasis: {
                  label: {
                    show: true,
                    color: '#fff',
                    fontSize: 50,
                  },
                  areaColor: {
                    image: this.$refs.mapSvg, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串
                    repeat: 'repeat', // 是否平铺, 可以是 'repeat-x', 'repeat-y', 'no-repeat'
                  },
                  borderColor: '#3fdaff',
                  borderWidth: 10,
                },
              },
              zlevel: 99,
              data: [],
            })

            this.myChartMap.setOption(option)
            this.myChartMap.off("click")
            this.myChartMap.on("click", this.echartsMapClick)
          })
        },

        echartsMapClick (e) {
          if (e.data) {
            // console.log('地图点击', e.data, this.cylList[this.cylIndex])
            let cyl = this.cylList[this.cylIndex] ? this.cylList[this.cylIndex].cylId : ''
            let qx = e.data.name
            this.queryQyList(cyl, qx)
          }
        },

        queryQyList (cyl, qx) {
          window.parent.lay.openIframe({
            type: 'openIframe',
            name: 'qyzf-qy-list',
            src: baseURL.url + '/static/citybrain/qyhx/commont/qyzf-qy-list.html',
            left: 'calc(50% - 1554px)',
            top: '190px',
            width: '3108px',
            height: '1900px',
            zIndex: '993',
            argument: {
              status: 'qyfbQyList',
              type: cyl,
              name: qx
            },
          })
        },
      }
    })
  </script>
</body>

</html>